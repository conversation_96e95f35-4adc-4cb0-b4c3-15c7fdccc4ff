// =========================================================================================
// Service for Partner Management - 商業夥伴管理服務 (遵循 ItemService 模式)
// =========================================================================================

import { Partner } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";
import { processApiResponse, safeString, safeBoolean } from '@/utils/dataValidation';

// =========================================================================================
// 資料驗證和工具函數 (參考 ItemService 模式)
// =========================================================================================

/**
 * 建立空的商業夥伴物件
 */
export const createEmptyPartner = (): Partial<Partner> => ({
    partnerID: '',
    individualDetail: undefined,
    organizationDetail: undefined,
    customerDetail: undefined,
    supplierDetail: undefined,
    partnerContacts: [],
    addresses: [],
});

/**
 * 驗證商業夥伴資料
 * @param data 商業夥伴資料
 * @returns 驗證結果
 */
const validatePartnerData = (data: any): { isValid: boolean; message?: string } => {
    if (!data || typeof data !== 'object') {
        return { isValid: false, message: "商業夥伴資料不能為空" };
    }

    // 檢查是否同時為個人和組織
    if (data.individualDetail && data.organizationDetail) {
        return { isValid: false, message: "商業夥伴不得同時為個人及組織" };
    }

    // 檢查是否至少有個人或組織資料
    if (!data.individualDetail && !data.organizationDetail) {
        return { isValid: false, message: "商業夥伴必須為個人或組織其中一種" };
    }

    // 驗證個人資料
    if (data.individualDetail) {
        if (!data.individualDetail.lastName || data.individualDetail.lastName.trim() === '') {
            return { isValid: false, message: "個人姓氏不能為空" };
        }
    }

    // 驗證組織資料
    if (data.organizationDetail) {
        if (!data.organizationDetail.companyName || data.organizationDetail.companyName.trim() === '') {
            return { isValid: false, message: "組織名稱不能為空" };
        }
    }

    return { isValid: true };
};

/**
 * 驗證單一商業夥伴資料
 * @param data API 回應的商業夥伴資料
 * @returns 驗證後的商業夥伴物件或 null
 */
const validateSinglePartner = (data: any): Partner | null => {
    if (!data || typeof data !== 'object' || !data.partnerID) {
        console.warn('⚠️ PartnerService: 無效的商業夥伴資料:', data);
        return null;
    }

    return {
        partnerID: safeString(data.partnerID),
        individualDetail: data.individualDetail || undefined,
        organizationDetail: data.organizationDetail || undefined,
        customerDetail: data.customerDetail || undefined,
        supplierDetail: data.supplierDetail || undefined,
        partnerContacts: Array.isArray(data.partnerContacts) ? data.partnerContacts : [],
        addresses: Array.isArray(data.addresses) ? data.addresses : [],
        createTime: data.createTime || null,
        createUserId: data.createUserId || null,
        updateTime: data.updateTime || null,
        updateUserId: data.updateUserId || null,
        deleteTime: data.deleteTime || null,
        deleteUserId: data.deleteUserId || null,
        isDeleted: safeBoolean(data.isDeleted),
    };
};

// =========================================================================================
// API 服務函數
// =========================================================================================

/**
 * 取得商業夥伴列表
 * @returns Promise<ApiResponse<Partner[]>>
 */
export async function getPartnerList(): Promise<ApiResponse<Partner[]>> {
    try {
        console.log('🔄 PartnerService: 開始載入商業夥伴列表...');
        const response = await httpClient(apiEndpoints.getPartnerList, {
            method: "GET",
        });

        console.log(`✅ PartnerService: API回應完成`, response);
        return response;
    } catch (error: any) {
        console.error('❌ PartnerService: 載入商業夥伴列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "搜尋商業夥伴列表失敗",
            data: []
        };
    }
}

/**
 * 取得單一商業夥伴
 * @param partnerID 商業夥伴ID
 * @returns Promise<ApiResponse<Partner>>
 */
export async function getPartner(partnerID: string): Promise<ApiResponse<Partner>> {
    try {
        if (!partnerID || typeof partnerID !== 'string') {
            return {
                success: false,
                message: "商業夥伴ID不能為空",
            };
        }

        console.log(`🔄 PartnerService: 載入商業夥伴詳細資料 (ID: ${partnerID})...`);
        const response = await httpClient(`${apiEndpoints.getPartner}/${partnerID}`, {
            method: "GET",
        });

        if (response.success) {
            console.log('✅ PartnerService: 商業夥伴載入成功');
        } else {
            console.warn('⚠️ PartnerService: 商業夥伴載入失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ PartnerService: 載入商業夥伴時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得商業夥伴失敗",
        };
    }
}

/**
 * 新增商業夥伴
 * @param partnerData 商業夥伴資料
 * @returns Promise<ApiResponse>
 */
export async function addPartner(partnerData: Partial<Partner>): Promise<ApiResponse> {
    try {
        // 資料驗證
        const validation = validatePartnerData(partnerData);
        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message || "商業夥伴資料驗證失敗",
            };
        }

        const partnerName = partnerData.organizationDetail?.companyName ||
                           `${partnerData.individualDetail?.lastName}${partnerData.individualDetail?.firstName}` ||
                           '未知夥伴';

        console.log('🔄 PartnerService: 新增商業夥伴...', partnerName);
        const response = await httpClient(apiEndpoints.addPartner, {
            method: "POST",
            body: JSON.stringify(partnerData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ PartnerService: 商業夥伴新增成功');
        } else {
            console.warn('⚠️ PartnerService: 商業夥伴新增失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ PartnerService: 新增商業夥伴時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "新增商業夥伴失敗",
        };
    }
}

/**
 * 更新商業夥伴
 * @param partnerData 商業夥伴資料
 * @returns Promise<ApiResponse>
 */
export async function editPartner(partnerData: Partial<Partner>): Promise<ApiResponse> {
    try {
        // 檢查必要的ID
        if (!partnerData.partnerID) {
            return {
                success: false,
                message: "商業夥伴ID不能為空",
            };
        }

        // 資料驗證
        const validation = validatePartnerData(partnerData);
        if (!validation.isValid) {
            return {
                success: false,
                message: validation.message || "商業夥伴資料驗證失敗",
            };
        }

        const partnerName = partnerData.organizationDetail?.companyName ||
                           `${partnerData.individualDetail?.lastName}${partnerData.individualDetail?.firstName}` ||
                           '未知夥伴';

        console.log('🔄 PartnerService: 更新商業夥伴...', partnerName);
        const response = await httpClient(apiEndpoints.editPartner, {
            method: "PUT",
            body: JSON.stringify(partnerData),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ PartnerService: 商業夥伴更新成功');
        } else {
            console.warn('⚠️ PartnerService: 商業夥伴更新失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ PartnerService: 更新商業夥伴時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "更新商業夥伴失敗",
        };
    }
}

/**
 * 刪除商業夥伴
 * @param partnerID 商業夥伴ID
 * @returns Promise<ApiResponse>
 */
export async function deletePartner(partnerID: string): Promise<ApiResponse> {
    try {
        if (!partnerID || typeof partnerID !== 'string') {
            return {
                success: false,
                message: "商業夥伴ID不能為空",
            };
        }

        console.log('🔄 PartnerService: 刪除商業夥伴...', partnerID);
        const response = await httpClient(apiEndpoints.deletePartner, {
            method: "DELETE",
            body: JSON.stringify({ partnerID: partnerID }),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ PartnerService: 商業夥伴刪除成功');
        } else {
            console.warn('⚠️ PartnerService: 商業夥伴刪除失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ PartnerService: 刪除商業夥伴時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "刪除商業夥伴失敗",
        };
    }
}