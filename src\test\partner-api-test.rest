# FastERP Partner API 測試檔案
# 使用 VS Code REST Client 擴展執行這些測試
# 
# 注意：這是可移除的測試代碼
# 測試完成後可以安全刪除此檔案

### 變數設定
@baseUrl = https://localhost:7137
@contentType = application/json
### 登入驗證
# @name Login
POST {{baseUrl}}/api/Login/VerifyLogin
Accept: application/json
Content-Type: application/json

{
    "Account": "FastAdmin",
    "Password": "fast!234"
}
### 1. 測試 Partner API 連接
GET {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

### 2. 測試 CustomerCategory API 連接
GET {{baseUrl}}/api/CustomerCategory/GetAll
Content-Type: {{contentType}}

### 3. 測試 SupplierCategory API 連接
GET {{baseUrl}}/api/SupplierCategory/GetAll
Content-Type: {{contentType}}

### 4. 新增測試客戶分類
POST {{baseUrl}}/api/CustomerCategory
Content-Type: {{contentType}}

{
  "name": "測試客戶分類",
  "description": "這是一個測試用的客戶分類",
  "parentID": null,
  "sortCode": 1
}

### 5. 新增測試供應商分類
POST {{baseUrl}}/api/SupplierCategory
Content-Type: {{contentType}}

{
  "name": "測試供應商分類",
  "description": "這是一個測試用的供應商分類",
  "parentID": null,
  "sortCode": 1
}

### 6. 新增測試個人夥伴 (客戶)
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "individualDetail": {
    "lastName": "測試",
    "firstName": "客戶",
    "identificationNumber": "A123456789"
  },
  "customerDetail": {
    "customerCode": "CUST001",
    "settlementDay": 15
  }
}

### 7. 新增測試組織夥伴 (供應商)
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "organizationDetail": {
    "companyName": "測試供應商公司",
    "bussinessId": "12345678",
    "taxId": "87654321",
    "responsiblePerson": "負責人姓名"
  },
  "supplierDetail": {
    "supplierCode": "SUPP001",
    "settlementDay": 30
  }
}

### 8. 新增測試組織夥伴 (客戶+供應商)
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "organizationDetail": {
    "companyName": "測試雙重角色公司",
    "bussinessId": "11111111",
    "taxId": "22222222",
    "responsiblePerson": "雙重角色負責人"
  },
  "customerDetail": {
    "customerCode": "CUST002",
    "settlementDay": 10
  },
  "supplierDetail": {
    "supplierCode": "SUPP002",
    "settlementDay": 20
  }
}

### 9. 測試錯誤處理 - 無效資料
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{
  "invalidField": "這應該會失敗"
}

### 10. 測試錯誤處理 - 空資料
POST {{baseUrl}}/api/Partner
Content-Type: {{contentType}}

{}

### 測試代碼移除指南
# 
# 此檔案包含可移除的測試代碼，測試完成後請執行以下步驟：
# 
# 1. 刪除此檔案：src/test/partner-api-test.rest
# 2. 如果有建立測試資料，請透過 Partner 管理介面刪除測試記錄
# 3. 確認沒有其他測試檔案殘留
# 
# 測試檔案識別標記：
# - 檔案名稱包含 "test" 關鍵字
# - 檔案內容包含 "測試" 或 "test" 相關字樣
# - 位於 src/test/ 目錄下的檔案
