/* === 測試代碼開始 - 可安全移除 === */
"use client";

import React, { useState } from 'react';
import { Card, Button, Space, message, Divider, Typography, Alert, Collapse, Tag } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';

// Services
import { getPartnerList, addPartner, editPartner, deletePartner } from '@/services/ims/PartnerService';
import { getCustomerCategoryList, addCustomerCategory } from '@/services/ims/CustomerCategoryService';
import { getSupplierCategoryList, addSupplierCategory } from '@/services/ims/SupplierCategoryService';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  duration?: number;
}

const PartnerTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // 更新測試結果
  const updateTestResult = (name: string, status: 'success' | 'error', message: string, duration?: number) => {
    setTestResults(prev => {
      const index = prev.findIndex(r => r.name === name);
      const newResult = { name, status, message, duration };
      
      if (index >= 0) {
        const newResults = [...prev];
        newResults[index] = newResult;
        return newResults;
      } else {
        return [...prev, newResult];
      }
    });
  };

  // 執行單一測試
  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    const startTime = Date.now();
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'success', '測試通過', duration);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'error', error.message || '測試失敗', duration);
    }
  };

  // 測試 Partner API
  const testPartnerAPI = async () => {
    console.log('🧪 開始測試 Partner API...');
    
    // 測試取得列表
    await runTest('Partner 列表查詢', async () => {
      const response = await getPartnerList();
      if (!response.success) {
        throw new Error(response.message || 'API 回應失敗');
      }
      console.log('✅ Partner 列表查詢成功:', response.data?.length || 0, '筆記錄');
    });

    // 測試新增個人夥伴
    await runTest('新增個人夥伴', async () => {
      const testPartner = {
        individualDetail: {
          lastName: '測試',
          firstName: '個人',
          identificationNumber: 'TEST123456'
        },
        customerDetail: {
          customerCode: 'TEST_CUST_001',
          settlementDay: 15
        }
      };

      const response = await addPartner(testPartner);
      if (!response.success) {
        throw new Error(response.message || '新增失敗');
      }
      console.log('✅ 個人夥伴新增成功');
    });

    // 測試新增組織夥伴
    await runTest('新增組織夥伴', async () => {
      const testPartner = {
        organizationDetail: {
          companyName: '測試公司有限公司',
          bussinessId: '12345678',
          taxId: '87654321',
          responsiblePerson: '測試負責人'
        },
        supplierDetail: {
          supplierCode: 'TEST_SUPP_001',
          settlementDay: 30
        }
      };

      const response = await addPartner(testPartner);
      if (!response.success) {
        throw new Error(response.message || '新增失敗');
      }
      console.log('✅ 組織夥伴新增成功');
    });
  };

  // 測試分類 API
  const testCategoryAPI = async () => {
    console.log('🧪 開始測試分類 API...');

    // 測試客戶分類
    await runTest('客戶分類查詢', async () => {
      const response = await getCustomerCategoryList();
      if (!response.success) {
        throw new Error(response.message || 'API 回應失敗');
      }
      console.log('✅ 客戶分類查詢成功:', response.data?.length || 0, '筆記錄');
    });

    await runTest('新增客戶分類', async () => {
      const testCategory = {
        name: '測試客戶分類',
        description: '這是測試用的客戶分類',
        sortCode: 999
      };

      const response = await addCustomerCategory(testCategory);
      if (!response.success) {
        throw new Error(response.message || '新增失敗');
      }
      console.log('✅ 客戶分類新增成功');
    });

    // 測試供應商分類
    await runTest('供應商分類查詢', async () => {
      const response = await getSupplierCategoryList();
      if (!response.success) {
        throw new Error(response.message || 'API 回應失敗');
      }
      console.log('✅ 供應商分類查詢成功:', response.data?.length || 0, '筆記錄');
    });

    await runTest('新增供應商分類', async () => {
      const testCategory = {
        name: '測試供應商分類',
        description: '這是測試用的供應商分類',
        sortCode: 999
      };

      const response = await addSupplierCategory(testCategory);
      if (!response.success) {
        throw new Error(response.message || '新增失敗');
      }
      console.log('✅ 供應商分類新增成功');
    });
  };

  // 執行所有測試
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      console.log('🚀 開始執行 Partner 模組整合測試...');
      
      await testPartnerAPI();
      await testCategoryAPI();
      
      console.log('🎉 所有測試執行完成');
      message.success('測試執行完成，請查看結果');
    } catch (error) {
      console.error('❌ 測試執行過程中發生錯誤:', error);
      message.error('測試執行失敗');
    } finally {
      setIsRunning(false);
    }
  };

  // 清除測試結果
  const clearResults = () => {
    setTestResults([]);
    message.info('測試結果已清除');
  };

  // 渲染測試結果
  const renderTestResult = (result: TestResult) => {
    const getStatusIcon = () => {
      switch (result.status) {
        case 'success':
          return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
        case 'error':
          return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
        default:
          return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      }
    };

    const getStatusColor = () => {
      switch (result.status) {
        case 'success':
          return 'success';
        case 'error':
          return 'error';
        default:
          return 'default';
      }
    };

    return (
      <div key={result.name} style={{ marginBottom: 8, padding: 8, border: '1px solid #f0f0f0', borderRadius: 4 }}>
        <Space>
          {getStatusIcon()}
          <Text strong>{result.name}</Text>
          <Tag color={getStatusColor()}>{result.status}</Tag>
          {result.duration && <Text type="secondary">({result.duration}ms)</Text>}
        </Space>
        <div style={{ marginTop: 4 }}>
          <Text type={result.status === 'error' ? 'danger' : 'secondary'}>
            {result.message}
          </Text>
        </div>
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <PlayCircleOutlined style={{ color: '#1890ff' }} />
          <span>Partner 模組 API 測試</span>
          <Tag color="orange">測試組件</Tag>
        </Space>
      }
      style={{ margin: '20px' }}
    >
      <Alert
        message="測試組件說明"
        description="此組件用於測試 Partner 模組的 API 整合功能。測試完成後可以安全移除此組件。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Space style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlayCircleOutlined />}
          onClick={runAllTests}
          loading={isRunning}
        >
          執行所有測試
        </Button>
        <Button onClick={clearResults}>
          清除結果
        </Button>
      </Space>

      {testResults.length > 0 && (
        <Collapse defaultActiveKey={['results']}>
          <Panel header={`測試結果 (${testResults.length} 項)`} key="results">
            <div style={{ maxHeight: 400, overflowY: 'auto' }}>
              {testResults.map(renderTestResult)}
            </div>
            
            <Divider />
            
            <div>
              <Text strong>測試統計：</Text>
              <Space>
                <Tag color="success">
                  成功: {testResults.filter(r => r.status === 'success').length}
                </Tag>
                <Tag color="error">
                  失敗: {testResults.filter(r => r.status === 'error').length}
                </Tag>
                <Tag color="default">
                  總計: {testResults.length}
                </Tag>
              </Space>
            </div>
          </Panel>
        </Collapse>
      )}

      <Divider />

      <Alert
        message="測試代碼移除指南"
        description={
          <div>
            <p>測試完成後，請執行以下步驟移除測試代碼：</p>
            <ol>
              <li>刪除此組件檔案：<code>src/app/ims/components/PartnerTestComponent.tsx</code></li>
              <li>刪除 REST 測試檔案：<code>src/test/partner-api-test.rest</code></li>
              <li>移除任何引用此組件的代碼</li>
              <li>清理測試過程中建立的測試資料</li>
            </ol>
          </div>
        }
        type="warning"
        showIcon
      />
    </Card>
  );
};

export default PartnerTestComponent;
/* === 測試代碼結束 === */
