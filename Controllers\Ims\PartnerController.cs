using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Ims;

/// <summary> 商業夥伴管理 </summary>
[ApiController]
[Route("api/[controller]")]
[SwaggerTag("商業夥伴管理")]
public class PartnerController(IPartnerService _partnerService) : ControllerBase
{
    /// <summary> 取得商業夥伴列表 </summary>
    [HttpGet]
    [SwaggerOperation(Summary = "取得商業夥伴列表", Description = "取得商業夥伴列表")]
    public async Task<IActionResult> GetAll()
    {
        var partners = await _partnerService.GetAllAsync();
        return Ok(partners);
    }

    /// <summary> 取得商業夥伴 </summary>
    [HttpGet]
    [Route("{PartnerID}")]
    [SwaggerOperation(Summary = "取得商業夥伴", Description = "取得商業夥伴")]
    public async Task<IActionResult> Get(Guid PartnerID)
    {
        try
        {
            var partner = await _partnerService.GetAsync(PartnerID);
            return Ok(partner);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary> 新增商業夥伴 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "新增商業夥伴", Description = "新增商業夥伴")]
    public async Task<IActionResult> Add([FromBody] PartnerDTO _data)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        try
        {
            var (result, msg) = await _partnerService.AddAsync(_data);
            return Ok(new { success = true, message = msg, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPut]
    [SwaggerOperation(Summary = "更新商業夥伴", Description = "更新商業夥伴")]
    public async Task<IActionResult> Update([FromBody] PartnerDTO dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        try
        {
            var updatedPartner = await _partnerService.UpdateAsync(dto);
            return Ok(updatedPartner);
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }

    [HttpDelete("{PartnerID}")]
    [SwaggerOperation(Summary = "刪除商業夥伴", Description = "刪除商業夥伴")]
    public async Task<IActionResult> Delete(Guid PartnerID)
    {
        try
        {
            await _partnerService.DeleteAsync(PartnerID);
            return NoContent();
        }
        catch (Exception ex)
        {
            return NotFound(ex.Message);
        }
    }
}