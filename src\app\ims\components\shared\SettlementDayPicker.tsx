"use client";

import React, { useState } from 'react';
import { DatePicker, Select, Space, Button, Tooltip } from 'antd';
import { CalendarOutlined, NumberOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';

interface SettlementDayPickerProps {
  value?: number;
  onChange?: (value: number | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
}

/**
 * 結算日期選擇器組件
 * 提供兩種選擇方式：日曆選擇和下拉選擇
 * 最終儲存為月份中的日期數字 (1-31)
 */
const SettlementDayPicker: React.FC<SettlementDayPickerProps> = ({
  value,
  onChange,
  placeholder = "請選擇結算日",
  disabled = false,
  style
}) => {
  const [mode, setMode] = useState<'calendar' | 'dropdown'>('dropdown');

  // 處理日曆選擇
  const handleCalendarChange = (date: Dayjs | null) => {
    if (date) {
      const day = date.date(); // 獲取日期中的天數 (1-31)
      onChange?.(day);
    } else {
      onChange?.(undefined);
    }
  };

  // 處理下拉選擇
  const handleDropdownChange = (day: number | undefined) => {
    onChange?.(day);
  };

  // 根據當前值創建日期對象用於日曆顯示
  const getDateValue = (): Dayjs | null => {
    if (!value) return null;
    // 使用當前月份和選定的日期創建日期對象
    const currentMonth = dayjs();
    return currentMonth.date(value);
  };

  // 禁用日期函數 - 只允許選擇當前月份的有效日期
  const disabledDate = (current: Dayjs) => {
    const currentMonth = dayjs();
    // 禁用非當前月份的日期
    return current.month() !== currentMonth.month() || current.year() !== currentMonth.year();
  };

  return (
    <div style={style}>
      <Space.Compact style={{ width: '100%' }}>
        {mode === 'calendar' ? (
          <DatePicker
            value={getDateValue()}
            onChange={handleCalendarChange}
            placeholder={placeholder}
            disabled={disabled}
            style={{ flex: 1 }}
            format="DD"
            disabledDate={disabledDate}
            picker="date"
            showToday={false}
            suffixIcon={<CalendarOutlined />}
          />
        ) : (
          <Select
            value={value}
            onChange={handleDropdownChange}
            placeholder={placeholder}
            disabled={disabled}
            style={{ flex: 1 }}
            allowClear
            suffixIcon={<NumberOutlined />}
          >
            {Array.from({ length: 31 }, (_, i) => (
              <Select.Option key={i + 1} value={i + 1}>
                每月 {i + 1} 日
              </Select.Option>
            ))}
          </Select>
        )}
        
        <Tooltip title={mode === 'calendar' ? '切換到下拉選擇' : '切換到日曆選擇'}>
          <Button
            icon={mode === 'calendar' ? <NumberOutlined /> : <CalendarOutlined />}
            onClick={() => setMode(mode === 'calendar' ? 'dropdown' : 'calendar')}
            disabled={disabled}
          />
        </Tooltip>
      </Space.Compact>
      
      {value && (
        <div style={{ 
          fontSize: '12px', 
          color: '#666', 
          marginTop: '4px',
          textAlign: 'center'
        }}>
          每月 {value} 日結算
        </div>
      )}
    </div>
  );
};

export default SettlementDayPicker;
