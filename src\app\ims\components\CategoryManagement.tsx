"use client";

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Modal, Form, Input, Button, Space, Tag, Row, Col, TreeSelect, Popconfirm, message, Card, Badge, Alert } from 'antd';
import type { InputRef } from 'antd';
import { PlusOutlined, EditOutlined, SaveOutlined, UndoOutlined, ApartmentOutlined, NodeIndexOutlined, ExpandOutlined, CompressOutlined, CaretRightOutlined, CaretDownOutlined, DeleteOutlined } from '@ant-design/icons';
import { ItemCategory } from '@/services/ims/ItemCategoryService';
import { addItemCategory, editItemCategory, deleteItemCategory } from '@/services/ims/ItemCategoryService';

interface CategoryManagementProps {
  visible: boolean;
  onClose: () => void;
  categories: ItemCategory[];
  categoryTreeData: any[];
  sortedCategoriesForDisplay: any[];
  onDataChange: () => void;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  visible,
  onClose,
  categories,
  categoryTreeData,
  sortedCategoriesForDisplay,
  onDataChange
}) => {
  // 狀態管理
  const [selectedCategory, setSelectedCategory] = useState<ItemCategory | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [operationMode, setOperationMode] = useState<'add' | 'edit' | 'addChild'>('add');
  const [parentCategoryForChild, setParentCategoryForChild] = useState<ItemCategory | null>(null);

  // 折疊功能狀態管理
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isAllExpanded, setIsAllExpanded] = useState(false);

  // Focus management refs
  const nameInputRef = useRef<InputRef>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);

  // 移動端檢測狀態
  const [isMobile, setIsMobile] = useState(false);

  // 驗證分類資料的輔助函數
  const validateCategoryData = (category: any): boolean => {
    return category &&
           typeof category === 'object' &&
           category.itemCategoryID &&
           typeof category.itemCategoryID === 'string' &&
           category.name &&
           typeof category.name === 'string';
  };

  // 滾動到輸入框的函數
  const scrollToInput = () => {
    setTimeout(() => {
      if (nameInputRef.current && formContainerRef.current) {
        // 先設置焦點
        nameInputRef.current.focus();

        // 然後滾動到輸入框
        const inputElement = nameInputRef.current.input;
        if (inputElement) {
          inputElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }
      }
    }, 150);
  };

  // 重置表單為新增模式
  const handleAddNew = () => {
    setSelectedCategory(null);
    setParentCategoryForChild(null);
    setOperationMode('add');
    form.resetFields();
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      console.log('🔄 提交分類資料:', values);

      const response = selectedCategory
        ? await editItemCategory({ ...selectedCategory, ...values })
        : await addItemCategory(values);

      if (response.success) {
        message.success(selectedCategory ? '分類更新成功' : '分類新增成功');
        handleAddNew();
        onDataChange();
        console.log('✅ 分類操作成功');
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('❌ 分類操作失敗:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除
  const handleDelete = async (categoryId: string) => {
    try {
      console.log('🔄 刪除分類:', categoryId);
      const response = await deleteItemCategory(categoryId);
      if (response.success) {
        message.success('分類刪除成功');
        onDataChange();
        console.log('✅ 分類刪除成功');
      } else {
        message.error(response.message || '刪除失敗');
      }
    } catch (error) {
      console.error('❌ 刪除分類失敗:', error);
      message.error('刪除失敗，請重試');
    }
  };

  // 處理編輯
  const handleEdit = (category: ItemCategory) => {
    setSelectedCategory(category);
    setParentCategoryForChild(null);
    setOperationMode('edit');
    console.log('🔄 編輯分類載入資料:', category);
    form.setFieldsValue(category);

    // 設置焦點並滾動到輸入框
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
        nameInputRef.current.select();
        scrollToInput();
      }
    }, 100);
  };

  // 處理快速建立子分類
  const handleAddChild = (parentCategory: ItemCategory) => {
    setSelectedCategory(null);
    setParentCategoryForChild(parentCategory);
    setOperationMode('addChild');
    form.resetFields();
    // 自動設定上級分類
    form.setFieldsValue({
      parentID: parentCategory.itemCategoryID
    });
    console.log('🔄 快速建立子分類，父分類:', parentCategory.name);

    // 設置焦點並滾動到輸入框
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
        scrollToInput();
      }
    }, 100);
  };

  // 折疊功能處理函數
  const toggleCategoryExpansion = (categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const expandAllCategories = () => {
    try {
      if (categories && Array.isArray(categories)) {
        const allCategoryIds = new Set(
          categories
            .filter(cat => cat && cat.itemCategoryID)
            .map(cat => cat.itemCategoryID)
        );
        setExpandedCategories(allCategoryIds);
        setIsAllExpanded(true);
      }
    } catch (error) {
      console.error('❌ CategoryManagement: 展開所有分類時發生錯誤:', error);
    }
  };

  const collapseAllCategories = () => {
    try {
      setExpandedCategories(new Set());
      setIsAllExpanded(false);
    } catch (error) {
      console.error('❌ CategoryManagement: 折疊所有分類時發生錯誤:', error);
    }
  };

  // 初始化展開狀態（預設展開第一層分類）
  useEffect(() => {
    if (visible && categories && Array.isArray(categories) && categories.length > 0) {
      try {
        const topLevelCategories = categories.filter(cat =>
          cat && typeof cat === 'object' && cat.itemCategoryID && !cat.parentID
        );
        const topLevelIds = new Set(topLevelCategories.map(cat => cat.itemCategoryID));
        setExpandedCategories(topLevelIds);
        setIsAllExpanded(false);
      } catch (error) {
        console.error('❌ CategoryManagement: 初始化展開狀態時發生錯誤:', error);
        setExpandedCategories(new Set());
        setIsAllExpanded(false);
      }
    }
  }, [visible, categories]);

  // 當互動視窗打開時，重置表單並設置焦點
  useEffect(() => {
    if (visible) {
      handleAddNew();
      // 延遲設置焦點並滾動，確保互動視窗完全渲染
      scrollToInput();
    }
  }, [visible]);

  // 當操作模式改變時設置焦點
  useEffect(() => {
    if (visible && (operationMode === 'edit' || operationMode === 'add' || operationMode === 'addChild')) {
      setTimeout(() => {
        if (nameInputRef.current) {
          nameInputRef.current.focus();
          // 如果是編輯模式，選中所有文字
          if (operationMode === 'edit') {
            nameInputRef.current.select();
          }
          scrollToInput();
        }
      }, 100);
    }
  }, [operationMode, visible]);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 操作模式配置
  const modeConfig = {
    add: {
      title: '新增商品分類',
      color: '#52c41a',
      icon: <PlusOutlined />,
      buttonText: '新增',
      cardTitle: '新增模式',
      description: '建立新的商品分類'
    },
    edit: {
      title: '編輯商品分類',
      color: '#1890ff',
      icon: <EditOutlined />,
      buttonText: '更新',
      cardTitle: '編輯模式',
      description: `正在編輯：${selectedCategory?.name || ''}`
    },
    addChild: {
      title: '新增子分類',
      color: '#722ed1',
      icon: <NodeIndexOutlined />,
      buttonText: '新增子分類',
      cardTitle: '子分類模式',
      description: `正在為「${parentCategoryForChild?.name || ''}」新增子分類`
    }
  };

  const currentMode = modeConfig[operationMode];

  // 根據展開狀態過濾要顯示的分類
  const visibleCategories = useMemo(() => {
    // 防禦性程式設計：確保 sortedCategoriesForDisplay 是有效的陣列
    if (!sortedCategoriesForDisplay || !Array.isArray(sortedCategoriesForDisplay)) {
      console.warn('⚠️ CategoryManagement: sortedCategoriesForDisplay 不是有效的陣列:', sortedCategoriesForDisplay);
      return [];
    }

    const shouldShowCategory = (category: any): boolean => {
      // 確保 category 物件有效
      if (!category || typeof category !== 'object') {
        return false;
      }

      // 頂級分類總是顯示
      if (category.level === 0) {
        return true;
      }

      // 子分類需要檢查父分類是否展開
      const parentCategory = sortedCategoriesForDisplay.find(cat =>
        cat && cat.itemCategoryID === category.parentID
      );

      if (!parentCategory) {
        return true; // 如果找不到父分類，預設顯示
      }

      // 檢查父分類是否展開
      const isParentExpanded = expandedCategories.has(parentCategory.itemCategoryID);

      if (!isParentExpanded) {
        return false; // 父分類未展開，不顯示此分類
      }

      // 如果父分類展開，遞迴檢查更上層的父分類
      if (parentCategory.level > 0) {
        return shouldShowCategory(parentCategory);
      }

      return true;
    };

    try {
      return sortedCategoriesForDisplay.filter(shouldShowCategory);
    } catch (error) {
      console.error('❌ CategoryManagement: 過濾可見分類時發生錯誤:', error);
      return [];
    }
  }, [sortedCategoriesForDisplay, expandedCategories]);

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ApartmentOutlined />
          <Badge color={currentMode.color} />
          <span>{currentMode.title}</span>
        </div>
      }
      open={visible}
      onCancel={() => {
        // 清理狀態並關閉
        handleAddNew();
        onClose();
      }}
      footer={[
        <Button key="close" onClick={() => {
          // 清理狀態並關閉
          handleAddNew();
          onClose();
        }}>
          關閉
        </Button>
      ]}
      width={isMobile ? '95%' : 900}
      style={isMobile ? { top: 20, margin: '0 auto' } : {}}
      maskClosable={true}
      keyboard={true}
      focusTriggerAfterClose={false}
    >
      <Row gutter={isMobile ? [8, 8] : [24, 16]}>

        {/* 左側：現有分類列表 */}
        <Col xs={24} lg={12}>
          <Card
            size="small"
            title="現有分類"
            extra={
              <Space>
                <Badge
                  count={categories && Array.isArray(categories) ? categories.length : 0}
                  style={{ backgroundColor: '#52c41a' }}
                />
                <Button
                  type="text"
                  size="small"
                  icon={isAllExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                  onClick={isAllExpanded ? collapseAllCategories : expandAllCategories}
                  style={{ fontSize: '12px' }}
                  disabled={!categories || !Array.isArray(categories) || categories.length === 0}
                >
                  {isAllExpanded ? '全部折疊' : '全部展開'}
                </Button>
              </Space>
            }
          >
            <div style={{
              maxHeight: 400,
              overflow: 'auto',
              padding: '4px'
            }}>
              {visibleCategories.map((category: any) => {
                // 驗證分類資料
                if (!validateCategoryData(category)) {
                  console.warn('⚠️ CategoryManagement: 無效的分類資料:', category);
                  return null;
                }
                // 動態計算階層樣式 - 支援無限層級
                const generateLevelStyle = (level: number) => {
                  // 基礎樣式配置
                  const baseStyles = [
                    { // Level 0 - 主分類
                      fontWeight: 'bold',
                      fontSize: '15px',
                      color: '#000',
                      backgroundColor: '#e6f7ff',
                      borderColor: '#1890ff',
                      borderWidth: '2px',
                      levelIndicator: { prefix: '', icon: null },
                      tagColor: 'blue'
                    },
                    { // Level 1 - 子分類
                      fontWeight: '500',
                      fontSize: '14px',
                      color: '#333',
                      backgroundColor: '#f6ffed',
                      borderColor: '#52c41a',
                      borderWidth: '1px',
                      levelIndicator: { prefix: '●', icon: '●', color: '#52c41a' },
                      tagColor: 'green'
                    },
                    { // Level 2 - 孫分類
                      fontWeight: 'normal',
                      fontSize: '13px',
                      color: '#666',
                      backgroundColor: '#fff7e6',
                      borderColor: '#fa8c16',
                      borderWidth: '1px',
                      levelIndicator: { prefix: '○', icon: '○', color: '#fa8c16' },
                      tagColor: 'orange'
                    }
                  ];

                  // 如果層級超過預定義樣式，動態生成
                  if (level < baseStyles.length) {
                    return baseStyles[level];
                  }

                  // 動態生成深層級樣式
                  const colorPalette = [
                    { bg: '#f9f0ff', border: '#722ed1', tag: 'purple' },
                    { bg: '#fff0f6', border: '#eb2f96', tag: 'magenta' },
                    { bg: '#f6ffed', border: '#52c41a', tag: 'lime' },
                    { bg: '#fff7e6', border: '#fa8c16', tag: 'gold' },
                    { bg: '#e6fffb', border: '#13c2c2', tag: 'cyan' },
                    { bg: '#f0f5ff', border: '#2f54eb', tag: 'geekblue' }
                  ];

                  const colorIndex = (level - baseStyles.length) % colorPalette.length;
                  const selectedColor = colorPalette[colorIndex];

                  // 計算字體大小（最小不低於11px）
                  const fontSize = Math.max(11, 15 - level);

                  // 生成層級指示器 - 使用現代化的視覺設計
                  const generateLevelIndicator = (depth: number) => {
                    if (depth === 0) return { prefix: '', icon: null };

                    // 使用簡潔的圓點和線條表示層級
                    const indicators = [
                      { prefix: '●', icon: '●', color: '#1890ff' }, // Level 1
                      { prefix: '○', icon: '○', color: '#52c41a' }, // Level 2
                      { prefix: '◦', icon: '◦', color: '#fa8c16' }, // Level 3
                      { prefix: '▪', icon: '▪', color: '#722ed1' }, // Level 4+
                    ];

                    const indicatorIndex = Math.min(depth - 1, indicators.length - 1);
                    return indicators[indicatorIndex];
                  };

                  const levelIndicator = generateLevelIndicator(level);

                  return {
                    fontWeight: level <= 1 ? (level === 0 ? 'bold' : '500') : 'normal',
                    fontSize: `${fontSize}px`,
                    color: level === 0 ? '#000' : level === 1 ? '#333' : '#666',
                    backgroundColor: selectedColor.bg,
                    borderColor: selectedColor.border,
                    borderWidth: level === 0 ? '2px' : '1px',
                    levelIndicator: levelIndicator,
                    tagColor: selectedColor.tag
                  };
                };

                const currentLevel = category.level || 0;
                const style = generateLevelStyle(currentLevel);
                const indentSize = 20; // 每層級縮排20px（稍微減少以適應更深層級）

                return (
                  <div
                    key={category.itemCategoryID}
                    style={{
                      marginBottom: 8,
                      paddingLeft: `${currentLevel * indentSize}px`,
                      position: 'relative'
                    }}
                  >
                    <Card
                      size="small"
                      style={{
                        border: selectedCategory?.itemCategoryID === category.itemCategoryID
                          ? '3px solid #722ed1'
                          : `${style.borderWidth} solid ${style.borderColor}`,
                        backgroundColor: selectedCategory?.itemCategoryID === category.itemCategoryID
                          ? '#f9f0ff'
                          : style.backgroundColor,
                        borderLeft: currentLevel > 0 ? `4px solid ${style.borderColor}` : `${style.borderWidth} solid ${style.borderColor}`,
                        boxShadow: selectedCategory?.itemCategoryID === category.itemCategoryID
                          ? '0 4px 12px rgba(114, 46, 209, 0.15)'
                          : '0 2px 8px rgba(0, 0, 0, 0.06)',
                        transition: 'all 0.3s ease',
                        width: '100%',
                        borderRadius: '8px'
                      }}
                      styles={{ body: { padding: '14px' } }}
                    >
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                      {/* 標題行 */}
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{
                            fontWeight: style.fontWeight,
                            fontSize: style.fontSize,
                            color: style.color,
                            marginBottom: '6px',
                            lineHeight: '1.4',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px'
                          }}>
                            {/* 折疊/展開按鈕（只有有子分類的分類才顯示） */}
                            {category.children && category.children.length > 0 && (
                              <Button
                                type="text"
                                size="small"
                                icon={expandedCategories.has(category.itemCategoryID) ? <CaretDownOutlined /> : <CaretRightOutlined />}
                                onClick={() => toggleCategoryExpansion(category.itemCategoryID)}
                                style={{
                                  padding: '0',
                                  width: '16px',
                                  height: '16px',
                                  minWidth: '16px',
                                  fontSize: '10px',
                                  color: style.borderColor
                                }}
                              />
                            )}
                            {style.levelIndicator && style.levelIndicator.icon && (
                              <span style={{
                                color: style.levelIndicator.color,
                                fontWeight: 'bold',
                                marginRight: '8px',
                                fontSize: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                minWidth: '16px'
                              }}>
                                {style.levelIndicator.icon}
                              </span>
                            )}
                            {category.name}
                          </div>
                          {category.description && (
                            <div style={{
                              fontSize: '12px',
                              color: '#8c8c8c',
                              lineHeight: '1.4',
                              fontStyle: currentLevel >= 2 ? 'italic' : 'normal'
                            }}>
                              {category.description}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 標籤行 */}
                      <div style={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: '6px',
                        alignItems: 'center'
                      }}>
                        <Tag
                          color={style.tagColor}
                          style={{
                            margin: 0,
                            fontSize: '11px'
                          }}
                        >
                          {'分類'} (L{currentLevel + 1})
                        </Tag>

                        {category.sortCode !== undefined && category.sortCode !== null && (
                          <Tag color="purple" style={{ margin: 0, fontSize: '11px' }}>
                            排序: {category.sortCode}
                          </Tag>
                        )}

                        {/* 顯示子分類數量 */}
                        {category.children && category.children.length > 0 && (
                          <Tag color="cyan" style={{ margin: 0, fontSize: '11px' }}>
                            子項: {category.children.length}
                          </Tag>
                        )}
                      </div>

                      {/* 操作按鈕行 */}
                      <div style={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        gap: '8px',
                        marginTop: '4px'
                      }}>
                        <Button
                          type="link"
                          size="small"
                          icon={<NodeIndexOutlined />}
                          onClick={() => handleAddChild(category)}
                          style={{
                            padding: '0 8px',
                            height: '24px',
                            fontSize: '12px',
                            color: '#722ed1'
                          }}
                        >
                          新增子分類
                        </Button>
                        <Button
                          type="link"
                          size="small"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(category)}
                          style={{
                            padding: '0 8px',
                            height: '24px',
                            fontSize: '12px'
                          }}
                        >
                          編輯
                        </Button>
                        <Popconfirm
                          title="確定要刪除此分類嗎？"
                          description={category.children && category.children.length > 0
                            ? "此分類包含子分類，刪除後子分類也會被刪除"
                            : "此操作無法復原"}
                          onConfirm={() => handleDelete(category.itemCategoryID)}
                          placement="topRight"
                        >
                          <Button
                            type="link"
                            danger
                            size="small"
                            style={{
                              padding: '0 8px',
                              height: '24px',
                              fontSize: '12px'
                            }}
                          >
                            刪除
                          </Button>
                        </Popconfirm>
                      </div>
                    </div>
                    </Card>
                  </div>
                );
              })}
              
              {(!categories || !Array.isArray(categories) || categories.length === 0) && (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 20px',
                  color: '#8c8c8c'
                }}>
                  <div style={{ fontSize: '14px' }}>
                    {!categories || !Array.isArray(categories) ? '分類資料載入中...' : '尚無商品分類'}
                  </div>
                  <div style={{ fontSize: '12px', marginTop: '4px' }}>
                    {!categories || !Array.isArray(categories) ? '請稍候' : '請點擊右側表單新增分類'}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </Col>

        {/* 右側：表單區域 */}
        <Col xs={24} lg={12}>
          <div ref={formContainerRef}>
          <Card
            size="small"
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {currentMode.icon}
                <span style={{ color: currentMode.color }}>{currentMode.cardTitle}</span>
              </div>
            }
            extra={
              <Button
                type="dashed"
                size="small"
                icon={<UndoOutlined />}
                onClick={handleAddNew}
              >
                重置為新增
              </Button>
            }
            style={{
              borderColor: currentMode.color,
              borderWidth: 2
            }}
          >
            <Alert
              message={currentMode.description}
              type={operationMode === 'add' ? 'success' : 'info'}
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
            >
              <Form.Item
                label="分類名稱"
                name="name"
                rules={[{ required: true, message: '請輸入分類名稱' }]}
              >
                <Input
                  ref={nameInputRef}
                  placeholder="請輸入分類名稱"
                  autoFocus={true}
                />
              </Form.Item>
              
              <Form.Item
                label="上級分類"
                name="parentID"
                tooltip="選擇上級分類，留空則為頂級分類"
              >
                <TreeSelect
                  placeholder="請選擇上級分類（可留空）"
                  allowClear
                  treeData={categoryTreeData}
                  treeDefaultExpandAll
                  showSearch
                  treeNodeFilterProp="title"
                  treeNodeLabelProp="title"
                  disabled={operationMode === 'addChild'}
                />
              </Form.Item>
              
              <Form.Item
                label="描述"
                name="description"
              >
                <Input.TextArea rows={3} placeholder="請輸入分類描述" />
              </Form.Item>
              
              <Form.Item
                label="排序碼"
                name="sortCode"
              >
                <Input type="number" placeholder="請輸入排序碼" />
              </Form.Item>

              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={operationMode === 'add' ? <PlusOutlined /> : <SaveOutlined />}
                style={{ 
                  marginTop: 16,
                  backgroundColor: currentMode.color,
                  borderColor: currentMode.color,
                  width: '100%'
                }}
              >
                {currentMode.buttonText}
              </Button>
            </Form>
          </Card>
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default CategoryManagement;
