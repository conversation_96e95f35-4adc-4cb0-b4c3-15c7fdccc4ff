using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Ims;
using Swashbuckle.AspNetCore.Annotations;

namespace FAST_ERP_Backend.Controllers.Ims;

/// <summary> 供應商分類管理 </summary>
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("供應商分類管理")]
public class SupplierCategoryController(ISupplierCategoryService _service) : ControllerBase
{
    /// <summary> 取得所有供應商分類 </summary>
    [HttpGet]
    [Route("GetAll")]
    [SwaggerOperation(Summary = "取得所有供應商分類", Description = "取得所有供應商分類列表")]
    public async Task<IActionResult> GetAllSupplierCategories()
    {
        try
        {
            var categories = await _service.GetAllAsync();
            return Ok(new { success = true, message = "取得供應商分類列表成功", data = categories });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得供應商分類列表失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 取得供應商分類詳細資料 </summary>
    [HttpGet]
    [Route("{categoryId}")]
    [SwaggerOperation(Summary = "取得供應商分類詳細資料", Description = "根據分類ID取得供應商分類詳細資料")]
    public async Task<IActionResult> GetSupplierCategoryDetail([FromRoute] Guid categoryId)
    {
        try
        {
            if (categoryId == Guid.Empty)
            {
                return BadRequest(new { success = false, message = "分類ID不能為空", data = (object?)null });
            }

            var category = await _service.GetByIdAsync(categoryId);
            if (category == null)
            {
                return NotFound(new { success = false, message = "找不到指定的供應商分類", data = (object?)null });
            }

            return Ok(new { success = true, message = "取得供應商分類詳細資料成功", data = category });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得供應商分類詳細資料失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 建立供應商分類 </summary>
    [HttpPost]
    [SwaggerOperation(Summary = "建立供應商分類", Description = "建立新的供應商分類")]
    public async Task<IActionResult> CreateSupplierCategory([FromBody] SupplierCategoryDTO categoryData)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, message = "資料驗證失敗", data = ModelState });
            }

            var (success, message) = await _service.AddAsync(categoryData);
            if (!success)
            {
                return BadRequest(new { success = false, message = message, data = (object?)null });
            }

            return Ok(new { success = true, message = message, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"建立供應商分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 更新供應商分類 </summary>
    [HttpPatch]
    [SwaggerOperation(Summary = "更新供應商分類", Description = "更新供應商分類資料")]
    public async Task<IActionResult> UpdateSupplierCategory([FromBody] SupplierCategoryDTO categoryData)
    {
        try
        {
            if (categoryData.SupplierCategoryID == Guid.Empty)
            {
                return BadRequest(new { success = false, message = "分類ID不能為空", data = (object?)null });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, message = "資料驗證失敗", data = ModelState });
            }

            var (success, message) = await _service.UpdateAsync(categoryData);
            if (!success)
            {
                return BadRequest(new { success = false, message = message, data = (object?)null });
            }

            return Ok(new { success = true, message = message, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"更新供應商分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 刪除供應商分類 </summary>
    [HttpDelete]
    [Route("{categoryId}")]
    [SwaggerOperation(Summary = "刪除供應商分類", Description = "刪除指定的供應商分類")]
    public async Task<IActionResult> DeleteSupplierCategory([FromRoute] Guid categoryId)
    {
        try
        {
            if (categoryId == Guid.Empty)
            {
                return BadRequest(new { success = false, message = "分類ID不能為空", data = (object?)null });
            }

            var (success, message) = await _service.DeleteAsync(categoryId);
            if (!success)
            {
                return BadRequest(new { success = false, message = message, data = (object?)null });
            }

            return Ok(new { success = true, message = message, data = (object?)null });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"刪除供應商分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 取得子分類 </summary>
    [HttpGet]
    [Route("{parentId}/Children")]
    [SwaggerOperation(Summary = "取得子分類", Description = "取得指定分類的子分類列表")]
    public async Task<IActionResult> GetChildrenCategories([FromRoute] Guid parentId)
    {
        try
        {
            if (parentId == Guid.Empty)
            {
                return BadRequest(new { success = false, message = "父分類ID不能為空", data = (object?)null });
            }

            var children = await _service.GetChildrenAsync(parentId);
            return Ok(new { success = true, message = "取得子分類成功", data = children });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得子分類失敗: {ex.Message}", data = (object?)null });
        }
    }

    /// <summary> 取得根分類 </summary>
    [HttpGet]
    [Route("Root")]
    [SwaggerOperation(Summary = "取得根分類", Description = "取得所有根分類列表")]
    public async Task<IActionResult> GetRootCategories()
    {
        try
        {
            var rootCategories = await _service.GetRootCategoriesAsync();
            return Ok(new { success = true, message = "取得根分類成功", data = rootCategories });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"取得根分類失敗: {ex.Message}", data = (object?)null });
        }
    }
}
