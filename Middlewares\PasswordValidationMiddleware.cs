using System.Security.Claims;
using FAST_ERP_Backend.Models.Common;
using Microsoft.IdentityModel.Tokens;

namespace FAST_ERP_Backend.Middlewares
{
    public class PasswordValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly TokenHandler _tokenHandler;
        private readonly IWebHostEnvironment _environment;
        private readonly bool _jwtValidationEnabled;

        public PasswordValidationMiddleware(RequestDelegate next, IConfiguration configuration, IWebHostEnvironment environment)
        {
            _next = next;
            _configuration = configuration;
            _environment = environment;
            _tokenHandler = new TokenHandler(configuration);

            // 讀取 JWT 驗證啟用設定，預設為 true
            _jwtValidationEnabled = _configuration.GetValue<bool>("AuthToken:JwtValidationEnabled", true);

            // 在開發環境中，如果設定為禁用 JWT 驗證，則記錄警告
            if (!_jwtValidationEnabled && _environment.IsDevelopment())
            {
                Console.WriteLine("⚠️ 警告: JWT 驗證已在開發環境中禁用。這僅應用於開發和測試目的。");
            }
            else if (!_jwtValidationEnabled && !_environment.IsDevelopment())
            {
                throw new InvalidOperationException("❌ 錯誤: 不允許在生產環境中禁用 JWT 驗證。請檢查 AuthToken:JwtValidationEnabled 設定。");
            }
        }

        public async Task InvokeAsync(HttpContext context)
        {
            //驗證路徑
            if (!ShouldValidatePassword(context))
            {
                // 如果 JWT 驗證被禁用（僅限開發環境），則跳過驗證
                if (!_jwtValidationEnabled)
                {
                    Console.WriteLine($"🔓 開發模式: 跳過 JWT 驗證 - 路徑: {context.Request.Path}");
                    await _next(context);
                    return;
                }

                // 先檢查是否帶有 Token,沒有則設成空字串
                var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last() ?? string.Empty;
                try
                {
                    // 驗證 Token
                    var claimsPrincipal = _tokenHandler.DecodeJwtToken(token);
                    context.User = claimsPrincipal;
                    await _next(context);
                    return;
                }
                catch (SecurityTokenException ex)
                {
                    Console.WriteLine($"❌ JWT 驗證失敗: {ex.Message} - 路徑: {context.Request.Path}");
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsync("請重新登入");
                    return;
                }
            }
            await _next(context);
        }
        //驗證路徑
        private bool ShouldValidatePassword(HttpContext context)
        {
            // 依需求新增其他路徑
            var protectedPaths = new[] {
                "/api/Login" ,
                "/Hubs/SignalRHub"
            };
            return protectedPaths.Any(path =>
                context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase));
        }
    }
}
