"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Card, Spin, Table, Button, Space, Modal, message, Tag, Tooltip, Popconfirm, Typography, Statistic, Row, Col, Progress, TreeSelect, Tabs, Badge } from "antd";
import { EditOutlined, DeleteOutlined, CheckCircleOutlined, PlusOutlined, ContactsOutlined, UserOutlined, TeamOutlined, SyncOutlined, SettingOutlined, UnorderedListOutlined, TagsOutlined, ShopOutlined } from "@ant-design/icons";

// Services and Types
import { Partner, CustomerCategory, SupplierCategory } from '@/services/ims/partner';
import { getPartnerList, getPartner, addPartner, editPartner, deletePartner, createEmptyPartner } from '@/services/ims/PartnerService';
import { getCustomerCategoryList, buildCustomerCategoryTree, getCustomerCategoryHierarchyName } from '@/services/ims/CustomerCategoryService';
import { getSupplierCategoryList, buildSupplierCategoryTree, getSupplierCategoryHierarchyName } from '@/services/ims/SupplierCategoryService';

// Components
import PartnerFormModal from '@/app/ims/components/PartnerFormModal';
import CustomerCategoryAdapter from '@/app/ims/components/shared/CustomerCategoryAdapter';
import SupplierCategoryAdapter from '@/app/ims/components/shared/SupplierCategoryAdapter';
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';
import { type FilterOption } from '@/app/ims/components/shared/AdvancedFilterComponent';

// Data Validation Utils
import { processApiResponse, safeString, safeBoolean } from '@/utils/dataValidation';

const { Title } = Typography;

// 整合資料介面
interface IntegratedData {
  partners: Partner[];
  customerCategories: CustomerCategory[];
  supplierCategories: SupplierCategory[];
}

// 統計資料介面
interface StatsData {
  totalPartners: number;
  customerCount: number;
  supplierCount: number;
  individualCount: number;
  organizationCount: number;
}



const PartnerPage = () => {
  // State - 遵循 Item 模組模式
  const [data, setData] = useState<IntegratedData>({
    partners: [],
    customerCategories: [],
    supplierCategories: [],
  });
  const [stats, setStats] = useState<StatsData>({
    totalPartners: 0,
    customerCount: 0,
    supplierCount: 0,
    individualCount: 0,
    organizationCount: 0,
  });
  const [filteredPartners, setFilteredPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isCustomerCategoryModalVisible, setIsCustomerCategoryModalVisible] = useState(false);
  const [isSupplierCategoryModalVisible, setIsSupplierCategoryModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('all');

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 移動端檢測 - 遵循 Item 模組模式
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Calculate stats - 遵循 Item 模組模式
  const calculateStats = useCallback((partnersData: Partner[]) => {
    const newStats: StatsData = {
      totalPartners: partnersData.length,
      customerCount: partnersData.filter(p => p.customerDetail != null).length,
      supplierCount: partnersData.filter(p => p.supplierDetail != null).length,
      individualCount: partnersData.filter(p => p.individualDetail != null).length,
      organizationCount: partnersData.filter(p => p.organizationDetail != null).length,
    };
    setStats(newStats);
  }, []);

  // 根據標籤頁篩選夥伴
  const filterPartnersByTab = useCallback((partners: Partner[], tab: string): Partner[] => {
    if (!Array.isArray(partners)) return [];

    switch (tab) {
      case 'all':
        return partners;
      case 'individual':
        return partners.filter(partner => partner.individualDetail);
      case 'organization':
        return partners.filter(partner => partner.organizationDetail);
      case 'customer':
        return partners.filter(partner => partner.customerDetail);
      case 'supplier':
        return partners.filter(partner => partner.supplierDetail);
      case 'both':
        return partners.filter(partner => partner.customerDetail && partner.supplierDetail);
      default:
        return partners;
    }
  }, []);

  // 載入所有資料 - 遵循 Item 模組模式
  const loadAllData = useCallback(async () => {
    setLoading(true);
    try {
      console.log('🔄 開始載入 Partner 模組資料...');

      // 並行載入所有資料
      const [partnersRes, customerCategoriesRes, supplierCategoriesRes] = await Promise.all([
        getPartnerList(),
        getCustomerCategoryList(),
        getSupplierCategoryList(),
      ]);

      // 處理 API 回應
      const partnersResult = processApiResponse<Partner>(partnersRes, '商業夥伴');
      const customerCategoriesResult = processApiResponse<CustomerCategory>(customerCategoriesRes, '客戶分類');
      const supplierCategoriesResult = processApiResponse<SupplierCategory>(supplierCategoriesRes, '供應商分類');

      // 更新狀態
      const newData: IntegratedData = {
        partners: partnersResult.data,
        customerCategories: customerCategoriesResult.data,
        supplierCategories: supplierCategoriesResult.data,
      };

      setData(newData);

      // 根據當前標籤頁篩選夥伴
      const tabFiltered = filterPartnersByTab(partnersResult.data, activeTab);
      setFilteredPartners(tabFiltered);

      // 計算統計資料
      calculateStats(partnersResult.data);

      console.log('✅ Partner 模組資料載入完成');
    } catch (error) {
      console.error('❌ 載入 Partner 模組資料時發生錯誤:', error);
      message.error('載入資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  }, [calculateStats, activeTab, filterPartnersByTab]);

  // Initial load
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  // Partner 篩選邏輯 - 遵循 Item 模組模式
  const applyPartnerFilters = useCallback((
    partners: Partner[],
    searchText: string,
    activeFilters: string[],
    filterValues: Record<string, any>
  ): Partner[] => {
    if (!Array.isArray(partners)) {
      console.warn('⚠️ partners 不是陣列:', partners);
      return [];
    }

    return partners.filter(partner => {
      if (!partner) return false;

      // 搜尋文字篩選
      const matchesSearch = !searchText ||
        safeString(partner.partnerID).toLowerCase().includes(searchText.toLowerCase()) ||
        (partner.individualDetail && safeString(partner.individualDetail.lastName).toLowerCase().includes(searchText.toLowerCase())) ||
        (partner.individualDetail && safeString(partner.individualDetail.firstName).toLowerCase().includes(searchText.toLowerCase())) ||
        (partner.organizationDetail && safeString(partner.organizationDetail.companyName).toLowerCase().includes(searchText.toLowerCase()));

      // 動態篩選條件
      const matchesFilters = activeFilters.every(filterKey => {
        const value = filterValues[filterKey];
        if (!value) return true;

        try {
          switch (filterKey) {
            case "type":
              return Array.isArray(value)
                ? value.some(v => (v === 'individual' && partner.individualDetail) || (v === 'organization' && partner.organizationDetail))
                : (value === 'individual' && partner.individualDetail) || (value === 'organization' && partner.organizationDetail);

            case "role":
              return Array.isArray(value)
                ? value.some(v => (v === 'customer' && partner.customerDetail) || (v === 'supplier' && partner.supplierDetail))
                : (value === 'customer' && partner.customerDetail) || (value === 'supplier' && partner.supplierDetail);

            case "customerCategory":
              if (!partner.customerDetail) return false;
              return Array.isArray(value)
                ? value.includes(partner.customerDetail.customerCategoryID)
                : value === partner.customerDetail.customerCategoryID;

            case "supplierCategory":
              if (!partner.supplierDetail) return false;
              return Array.isArray(value)
                ? value.includes(partner.supplierDetail.supplierCategoryID)
                : value === partner.supplierDetail.supplierCategoryID;

            default:
              return true;
          }
        } catch (error) {
          console.warn(`⚠️ 篩選條件 ${filterKey} 處理錯誤:`, error);
          return true;
        }
      });

      return matchesSearch && matchesFilters;
    });
  }, [data.customerCategories, data.supplierCategories]);

  // 標籤頁配置
  const tabItems = useMemo(() => {
    return [
      {
        key: 'all',
        label: (
          <Space>
            <UnorderedListOutlined />
            <span>全部夥伴</span>
            <Badge count={data.partners.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        ),
      },
      {
        key: 'individual',
        label: (
          <Space>
            <UserOutlined />
            <span>個人夥伴</span>
            <Badge count={stats.individualCount} style={{ backgroundColor: '#1890ff' }} />
          </Space>
        ),
      },
      {
        key: 'organization',
        label: (
          <Space>
            <TeamOutlined />
            <span>組織夥伴</span>
            <Badge count={stats.organizationCount} style={{ backgroundColor: '#722ed1' }} />
          </Space>
        ),
      },
      {
        key: 'customer',
        label: (
          <Space>
            <ContactsOutlined />
            <span>客戶</span>
            <Badge count={stats.customerCount} style={{ backgroundColor: '#fa8c16' }} />
          </Space>
        ),
      },
      {
        key: 'supplier',
        label: (
          <Space>
            <ShopOutlined />
            <span>供應商</span>
            <Badge count={stats.supplierCount} style={{ backgroundColor: '#13c2c2' }} />
          </Space>
        ),
      },
      {
        key: 'both',
        label: (
          <Space>
            <SettingOutlined />
            <span>客戶+供應商</span>
            <Badge
              count={data.partners.filter(p => p.customerDetail && p.supplierDetail).length}
              style={{ backgroundColor: '#eb2f96' }}
            />
          </Space>
        ),
      },
    ];
  }, [data.partners, stats, activeTab, filterPartnersByTab]);

  // Partner 篩選選項 - 遵循 Item 模組模式
  const partnerFilterOptions: FilterOption[] = useMemo(() => [
    {
      label: "夥伴類型",
      value: "type",
      type: "select",
      children: [
        { label: "個人", value: "individual" },
        { label: "組織", value: "organization" }
      ]
    },
    {
      label: "角色",
      value: "role",
      type: "select",
      children: [
        { label: "客戶", value: "customer" },
        { label: "供應商", value: "supplier" }
      ]
    },
    {
      label: "客戶分類",
      value: "customerCategory",
      type: "treeSelect",
      treeData: buildCustomerCategoryTree(data.customerCategories).map(category => ({
        title: category.name,
        value: category.customerCategoryID,
        key: category.customerCategoryID,
        children: category.children?.map(child => ({
          title: child.name,
          value: child.customerCategoryID,
          key: child.customerCategoryID,
        })) || []
      }))
    },
    {
      label: "供應商分類",
      value: "supplierCategory",
      type: "treeSelect",
      treeData: buildSupplierCategoryTree(data.supplierCategories).map(category => ({
        title: category.name,
        value: category.supplierCategoryID,
        key: category.supplierCategoryID,
        children: category.children?.map(child => ({
          title: child.name,
          value: child.supplierCategoryID,
          key: child.supplierCategoryID,
        })) || []
      }))
    }
  ], [data.customerCategories, data.supplierCategories]);

  // CRUD Operations
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const response = selectedPartner
        ? await editPartner({ ...values, partnerID: selectedPartner.partnerID })
        : await addPartner(values);

      if (response.success) {
        message.success(selectedPartner ? '更新成功' : '新增成功');
        setIsModalVisible(false);
        setSelectedPartner(null);
        await loadAllData();
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('提交商業夥伴資料時發生錯誤:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (partner: Partner) => {
    setSelectedPartner(partner);
    setIsModalVisible(true);
  };

  const handleDelete = async (partnerId: string) => {
    Modal.confirm({
      title: '確認刪除',
      content: '確定要刪除此商業夥伴嗎？此操作無法復原。',
      okText: '確定刪除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        const response = await deletePartner(partnerId);
        if (response.success) {
          message.success('刪除成功');
          await loadAllData();
        } else {
          message.error(response.message || '刪除失敗');
        }
      }
    });
  };

  // Table columns
  const columns = useMemo(() => [
    {
      title: '夥伴ID',
      dataIndex: 'partnerID',
      key: 'partnerID',
      width: 150,
      sorter: (a: Partner, b: Partner) => safeString(a.partnerID).localeCompare(safeString(b.partnerID)),
      render: (text: string) => <span style={{ fontWeight: 500 }}>{text || '-'}</span>,
    },
    {
      title: '夥伴名稱',
      key: 'name',
      sorter: (a: Partner, b: Partner) => safeString(a.organizationDetail?.companyName || a.individualDetail?.lastName).localeCompare(safeString(b.organizationDetail?.companyName || b.individualDetail?.lastName)),
      render: (record: Partner) => (
        <span style={{ fontWeight: 500, color: '#1890ff', cursor: 'pointer' }}
              onClick={() => handleEdit(record)}>
          {record.organizationDetail?.companyName || `${record.individualDetail?.lastName}${record.individualDetail?.firstName}` || '-'}
        </span>
      ),
    },
    {
      title: '類型',
      key: 'type',
      width: 100,
      render: (record: Partner) => (
        <Tag color={record.individualDetail ? 'blue' : 'green'} icon={record.individualDetail ? <UserOutlined /> : <TeamOutlined />}>
          {record.individualDetail ? '個人' : '組織'}
        </Tag>
      ),
    },
    {
      title: '角色',
      key: 'role',
      width: 120,
      render: (record: Partner) => (
        <Space direction="vertical" size="small">
          {record.customerDetail && (
            <Tag color="cyan" icon={<ContactsOutlined />}>
              客戶
            </Tag>
          )}
          {record.supplierDetail && (
            <Tag color="orange" icon={<ShopOutlined />}>
              供應商
            </Tag>
          )}
          {!record.customerDetail && !record.supplierDetail && (
            <Tag color="default">
              無角色
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: '分類',
      key: 'category',
      width: 150,
      render: (record: Partner) => (
        <Space direction="vertical" size="small">
          {record.customerDetail?.customerCategory && (
            <Tag color="blue">
              客戶: {record.customerDetail.customerCategory.name}
            </Tag>
          )}
          {record.supplierDetail?.supplierCategory && (
            <Tag color="orange">
              供應商: {record.supplierDetail.supplierCategory.name}
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      fixed: 'right' as const,
      render: (record: Partner) => (
        <Space size="small">
          <Tooltip title="編輯">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="刪除">
            <Popconfirm
              title="確定要刪除此商業夥伴嗎？"
              onConfirm={() => handleDelete(record.partnerID)}
              okText="確定"
              cancelText="取消"
            >
              <Button
                type="link"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ], [handleEdit, handleDelete]);

  return (
    <div style={{ padding: '24px' }}>
      <Spin spinning={loading}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '12px' }}>
            <ContactsOutlined style={{ color: '#1890ff' }} />
            商業夥伴管理
          </Title>
          <Typography.Text type="secondary">
            管理所有商業夥伴資料
          </Typography.Text>
        </div>

        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="總夥伴數" value={stats.totalPartners} prefix={<ContactsOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="客戶數量" value={stats.customerCount} prefix={<ContactsOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="供應商數量" value={stats.supplierCount} prefix={<ShopOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="個人夥伴" value={stats.individualCount} prefix={<UserOutlined />} /></Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card size="small"><Statistic title="組織夥伴" value={stats.organizationCount} prefix={<TeamOutlined />} /></Card>
          </Col>
        </Row>

        <Card size="small" style={{ marginBottom: '16px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setSelectedPartner(null);
                    setIsModalVisible(true);
                  }}
                >
                  新增夥伴
                </Button>
                <Button
                  icon={<TagsOutlined />}
                  onClick={() => setIsCustomerCategoryModalVisible(true)}
                >
                  客戶分類
                </Button>
                <Button
                  icon={<TagsOutlined />}
                  onClick={() => setIsSupplierCategoryModalVisible(true)}
                >
                  供應商分類
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={loadAllData}
                >
                  重新載入
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 動態搜尋和篩選 - 使用新的簡化組件 */}
        <FilterSearchContainer
          title="篩選與搜尋"
          filterOptions={partnerFilterOptions}
          searchPlaceholder={isMobile ? "搜尋夥伴" : "搜尋夥伴名稱、編號"}
          showStats={true}
          stats={{
            total: filterPartnersByTab(data.partners, activeTab).length,
            filtered: filteredPartners.length
          }}
          showClearMessage={true}
          clearMessage="已清除所有商業夥伴篩選條件"
          onFilterResult={(state) => {
            // 先根據標籤頁篩選
            const tabFiltered = filterPartnersByTab(data.partners, activeTab);

            // 再應用搜尋和進階篩選
            const filtered = applyPartnerFilters(
              tabFiltered,
              state.searchText,
              state.activeFilters,
              state.filterValues
            );
            setFilteredPartners(filtered);
            setCurrentPage(1); // 重置分頁

            console.log('🔍 商業夥伴篩選結果:', {
              total: data.partners.length,
              tabFiltered: tabFiltered.length,
              filtered: filtered.length,
              activeTab,
              state
            });
          }}
          compact={isMobile}
          className="mb-6"
        />

        {/* 夥伴分類標籤頁 */}
        <Card size="small" style={{ marginBottom: '16px' }}>
          <Tabs
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key);
              setCurrentPage(1); // 重置分頁

              // 重新應用篩選
              const tabFiltered = filterPartnersByTab(data.partners, key);
              setFilteredPartners(tabFiltered);

              console.log('🏷️ 切換標籤頁:', {
                newTab: key,
                totalPartners: data.partners.length,
                filteredCount: tabFiltered.length
              });
            }}
            items={tabItems}
            type="card"
            size={isMobile ? 'small' : 'middle'}
          />
        </Card>

        {/* 商業夥伴列表 */}
        <Card
          title={
            <Space>
              <span>商業夥伴列表</span>
              <Tag color="blue">
                {activeTab === 'all' ? '全部' :
                 activeTab === 'individual' ? '個人' :
                 activeTab === 'organization' ? '組織' :
                 activeTab === 'customer' ? '客戶' :
                 activeTab === 'supplier' ? '供應商' :
                 activeTab === 'both' ? '客戶+供應商' : ''}
              </Tag>
            </Space>
          }
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setSelectedPartner(null);
                  setIsModalVisible(true);
                }}
              >
                新增夥伴
              </Button>
            </Space>
          }
        >
          <Table
            columns={columns}
            dataSource={filteredPartners}
            rowKey="partnerID"
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: filteredPartners.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size);
              },
            }}
            scroll={{ x: 800 }}
            size={isMobile ? 'small' : 'middle'}
          />
        </Card>
      </Spin>

      <PartnerFormModal
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          setSelectedPartner(null);
        }}
        selectedPartner={selectedPartner}
        onSubmit={handleSubmit}
        loading={loading}
        customerCategories={data.customerCategories}
        supplierCategories={data.supplierCategories}
        onCategoryDataChange={loadAllData}
      />

      {/* 客戶分類管理 Modal */}
      <CustomerCategoryAdapter
        visible={isCustomerCategoryModalVisible}
        onClose={() => setIsCustomerCategoryModalVisible(false)}
        categories={data.customerCategories}
        onDataChange={loadAllData}
      />

      {/* 供應商分類管理 Modal */}
      <SupplierCategoryAdapter
        visible={isSupplierCategoryModalVisible}
        onClose={() => setIsSupplierCategoryModalVisible(false)}
        categories={data.supplierCategories}
        onDataChange={loadAllData}
      />
    </div>
  );
};

export default PartnerPage;