// =========================================================================================
// Service for Supplier Category Management - 供應商分類管理服務
// =========================================================================================

import { SupplierCategory } from '@/services/ims/partner';
import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";

// 建立空的供應商分類
export const createEmptySupplierCategory = (): Partial<SupplierCategory> => ({
    name: '',
    description: '',
    parentID: null,
    sortCode: 0,
});

/**
 * 取得供應商分類列表
 * @returns Promise<ApiResponse<SupplierCategory[]>>
 */
export async function getSupplierCategoryList(): Promise<ApiResponse<SupplierCategory[]>> {
    try {
        console.log('🔄 SupplierCategoryService: 開始載入供應商分類列表...');
        const response = await httpClient(apiEndpoints.getSupplierCategoryList, {
            method: "GET",
        });

        console.log(`✅ SupplierCategoryService: API回應完成`, response);
        return response;
    } catch (error: any) {
        console.error('❌ SupplierCategoryService: 載入供應商分類列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得供應商分類列表失敗",
            data: []
        };
    }
}

/**
 * 取得單一供應商分類
 * @param categoryId 分類ID
 * @returns Promise<ApiResponse<SupplierCategory>>
 */
export async function getSupplierCategory(categoryId: string): Promise<ApiResponse<SupplierCategory>> {
    try {
        if (!categoryId || typeof categoryId !== 'string') {
            return {
                success: false,
                message: "供應商分類ID不能為空",
            };
        }

        console.log(`🔄 SupplierCategoryService: 載入供應商分類詳細資料 (ID: ${categoryId})...`);
        const response = await httpClient(`${apiEndpoints.getSupplierCategory}/${categoryId}`, {
            method: "GET",
        });

        if (response.success) {
            console.log('✅ SupplierCategoryService: 供應商分類載入成功');
        } else {
            console.warn('⚠️ SupplierCategoryService: 供應商分類載入失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ SupplierCategoryService: 載入供應商分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得供應商分類失敗",
        };
    }
}

/**
 * 新增供應商分類
 * @param categoryData 分類資料
 * @returns Promise<ApiResponse>
 */
export async function addSupplierCategory(categoryData: Partial<SupplierCategory>): Promise<ApiResponse> {
    try {
        // 資料驗證
        if (!categoryData.name || categoryData.name.trim() === '') {
            return {
                success: false,
                message: "分類名稱不能為空",
            };
        }

        console.log('🔄 SupplierCategoryService: 新增供應商分類...', categoryData.name);
        const response = await httpClient(apiEndpoints.addSupplierCategory, {
            method: "POST",
            body: JSON.stringify({
                name: categoryData.name,
                description: categoryData.description || '',
                parentID: categoryData.parentID || null,
                sortCode: categoryData.sortCode || 0,
            }),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ SupplierCategoryService: 供應商分類新增成功');
        } else {
            console.warn('⚠️ SupplierCategoryService: 供應商分類新增失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ SupplierCategoryService: 新增供應商分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "新增供應商分類失敗",
        };
    }
}

/**
 * 修改供應商分類
 * @param categoryData 分類資料
 * @returns Promise<ApiResponse>
 */
export async function editSupplierCategory(categoryData: Partial<SupplierCategory>): Promise<ApiResponse> {
    try {
        // 檢查必要的ID
        if (!categoryData.supplierCategoryID) {
            return {
                success: false,
                message: "供應商分類ID不能為空",
            };
        }

        // 資料驗證
        if (!categoryData.name || categoryData.name.trim() === '') {
            return {
                success: false,
                message: "分類名稱不能為空",
            };
        }

        console.log('🔄 SupplierCategoryService: 更新供應商分類...', categoryData.name);
        const response = await httpClient(apiEndpoints.editSupplierCategory, {
            method: "PATCH",
            body: JSON.stringify({
                supplierCategoryID: categoryData.supplierCategoryID,
                name: categoryData.name,
                description: categoryData.description || '',
                parentID: categoryData.parentID || null,
                sortCode: categoryData.sortCode || 0,
            }),
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ SupplierCategoryService: 供應商分類更新成功');
        } else {
            console.warn('⚠️ SupplierCategoryService: 供應商分類更新失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ SupplierCategoryService: 更新供應商分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "更新供應商分類失敗",
        };
    }
}

/**
 * 刪除供應商分類
 * @param categoryId 分類ID
 * @returns Promise<ApiResponse>
 */
export async function deleteSupplierCategory(categoryId: string): Promise<ApiResponse> {
    try {
        if (!categoryId || typeof categoryId !== 'string') {
            return {
                success: false,
                message: "供應商分類ID不能為空",
            };
        }

        console.log('🔄 SupplierCategoryService: 刪除供應商分類...', categoryId);
        const response = await httpClient(`${apiEndpoints.deleteSupplierCategory}/${categoryId}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
        });

        if (response.success) {
            console.log('✅ SupplierCategoryService: 供應商分類刪除成功');
        } else {
            console.warn('⚠️ SupplierCategoryService: 供應商分類刪除失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ SupplierCategoryService: 刪除供應商分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "刪除供應商分類失敗",
        };
    }
}

// =========================================================================================
// 分類樹狀結構處理函數 (參考 ItemCategoryService 實現)
// =========================================================================================

/**
 * 建立分類樹狀結構
 * @param categories 分類列表
 * @returns 樹狀結構的分類列表
 */
export function buildSupplierCategoryTree(categories: SupplierCategory[]): SupplierCategory[] {
    if (!Array.isArray(categories)) {
        console.warn('⚠️ buildSupplierCategoryTree: categories 不是陣列');
        return [];
    }

    const categoryMap = new Map<string, SupplierCategory>();
    const rootCategories: SupplierCategory[] = [];

    // 建立 Map 以便快速查找
    categories.forEach(category => {
        categoryMap.set(category.supplierCategoryID, { ...category, children: [] });
    });

    // 建立樹狀結構
    categories.forEach(category => {
        const categoryNode = categoryMap.get(category.supplierCategoryID);
        if (!categoryNode) return;

        if (category.parentID && categoryMap.has(category.parentID)) {
            const parent = categoryMap.get(category.parentID);
            if (parent) {
                parent.children.push(categoryNode);
            }
        } else {
            rootCategories.push(categoryNode);
        }
    });

    return rootCategories;
}

/**
 * 取得分類階層名稱
 * @param categoryId 分類ID
 * @param categories 分類列表
 * @returns 階層名稱字串
 */
export function getSupplierCategoryHierarchyName(categoryId: string | null, categories: SupplierCategory[]): string {
    if (!categoryId || !Array.isArray(categories)) {
        return '';
    }

    const category = categories.find(c => c.supplierCategoryID === categoryId);
    if (!category) {
        return '';
    }

    const hierarchy: string[] = [];
    let current: SupplierCategory | undefined = category;

    while (current) {
        hierarchy.unshift(current.name);
        current = current.parentID ? categories.find(c => c.supplierCategoryID === current?.parentID) : undefined;
    }

    return hierarchy.join(' > ');
}
